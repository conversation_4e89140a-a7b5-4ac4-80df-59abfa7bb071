// Main entry point for the MHA game

document.addEventListener('DOMContentLoaded', function() {
    // Get canvas element
    const canvas = document.getElementById('gameCanvas');
    
    if (!canvas) {
        console.error('Canvas element not found!');
        return;
    }
    
    // Initialize the game
    const game = new Game(canvas);
    
    // Make game globally accessible for debugging and cross-script communication
    window.game = game;
    
    // Handle window resize
    function handleResize() {
        const container = document.getElementById('gameContainer');
        const containerRect = container.getBoundingClientRect();
        
        // Maintain aspect ratio
        const aspectRatio = 1024 / 768;
        let newWidth = window.innerWidth * 0.9;
        let newHeight = newWidth / aspectRatio;
        
        if (newHeight > window.innerHeight * 0.9) {
            newHeight = window.innerHeight * 0.9;
            newWidth = newHeight * aspectRatio;
        }
        
        // Don't make it too small
        newWidth = Math.max(640, newWidth);
        newHeight = Math.max(480, newHeight);
        
        canvas.style.width = newWidth + 'px';
        canvas.style.height = newHeight + 'px';
    }
    
    // Initial resize
    handleResize();
    
    // Handle window resize events
    window.addEventListener('resize', handleResize);
    
    // Prevent context menu on canvas
    canvas.addEventListener('contextmenu', (e) => {
        e.preventDefault();
    });
    
    // Handle visibility change (pause when tab is not active)
    document.addEventListener('visibilitychange', function() {
        if (document.hidden && game.state === 'playing') {
            game.pause();
        }
    });
    
    // Add some helpful keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // F11 for fullscreen
        if (e.key === 'F11') {
            e.preventDefault();
            toggleFullscreen();
        }
        
        // R for restart (when in game over state)
        if (e.key === 'r' || e.key === 'R') {
            if (game.state === 'gameOver') {
                game.restart();
            }
        }
        
        // M for main menu
        if (e.key === 'm' || e.key === 'M') {
            if (game.state === 'gameOver' || game.state === 'paused') {
                game.showMainMenu();
            }
        }
    });
    
    // Fullscreen functionality
    function toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('Error attempting to enable fullscreen:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }
    
    // Handle fullscreen changes
    document.addEventListener('fullscreenchange', function() {
        setTimeout(handleResize, 100); // Small delay to ensure proper sizing
    });
    
    // Performance monitoring (optional)
    let frameCount = 0;
    let lastFPSTime = performance.now();
    let fps = 60;
    
    function updateFPS() {
        frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - lastFPSTime >= 1000) {
            fps = Math.round((frameCount * 1000) / (currentTime - lastFPSTime));
            frameCount = 0;
            lastFPSTime = currentTime;
            
            // Log FPS if it's too low (for debugging)
            if (fps < 30) {
                console.warn('Low FPS detected:', fps);
            }
        }
    }
    
    // Add FPS counter to the game loop
    const originalRun = game.run;
    game.run = function() {
        const gameLoop = (currentTime) => {
            updateFPS();
            this.update(currentTime);
            this.draw();
            requestAnimationFrame(gameLoop);
        };
        
        requestAnimationFrame(gameLoop);
    };
    
    // Show loading message
    console.log('🦸‍♂️ My Hero Academia Game Loaded!');
    console.log('Controls:');
    console.log('  WASD/Arrow Keys: Move');
    console.log('  Space: Jump');
    console.log('  X: Basic Attack');
    console.log('  Z: Quirk Attack');
    console.log('  Escape: Pause');
    console.log('  F11: Fullscreen');
    console.log('  R: Restart (when game over)');
    console.log('  M: Main Menu');
    
    // Add some easter eggs for MHA fans
    const mhaQuotes = [
        "Plus Ultra!",
        "I am here!",
        "A real hero always finds a way for justice to be served!",
        "Whether you win or lose, you can always come out ahead by learning from the experience.",
        "Sometimes I do feel like I'm a failure. Like there's no hope for me. But even so, I'm not gonna give up.",
        "If you feel yourself hitting up against your limit, remember for what cause you clench your fists!"
    ];
    
    // Show random quote
    const randomQuote = mhaQuotes[Math.floor(Math.random() * mhaQuotes.length)];
    console.log(`💪 "${randomQuote}"`);
    
    // Add cheat codes for testing (remove in production)
    let cheatSequence = '';
    document.addEventListener('keydown', function(e) {
        cheatSequence += e.key.toLowerCase();
        
        // Keep only last 10 characters
        if (cheatSequence.length > 10) {
            cheatSequence = cheatSequence.slice(-10);
        }
        
        // Check for cheat codes
        if (cheatSequence.includes('plusultra')) {
            if (game.player) {
                game.player.health = game.player.maxHealth;
                game.player.quirkEnergy = game.player.maxQuirkEnergy;
                game.showMessage('Plus Ultra! Health and Quirk restored!', 2000);
                console.log('🦸‍♂️ Plus Ultra cheat activated!');
            }
            cheatSequence = '';
        }
        
        if (cheatSequence.includes('allmight')) {
            if (game.player) {
                game.player.addScore(1000);
                game.showMessage('All Might\'s blessing! +1000 points!', 2000);
                console.log('💪 All Might cheat activated!');
            }
            cheatSequence = '';
        }
    });
    
    // Start the game loop
    game.run();
    
    // Add some helpful tips to the console
    setTimeout(() => {
        console.log('🎮 Game Tips:');
        console.log('  • Combine attacks for higher combo scores');
        console.log('  • Use quirk attacks for more damage and area effect');
        console.log('  • Jump on platforms to avoid enemy attacks');
        console.log('  • Your quirk energy regenerates over time');
        console.log('  • Level up by defeating enemies to get stronger');
        console.log('  • Try the cheat codes: "plusultra" and "allmight"');
    }, 2000);
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Game };
}
