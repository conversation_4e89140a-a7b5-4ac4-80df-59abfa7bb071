// Utility functions for the MHA game

class Vector2 {
    constructor(x = 0, y = 0) {
        this.x = x;
        this.y = y;
    }
    
    add(other) {
        return new Vector2(this.x + other.x, this.y + other.y);
    }
    
    subtract(other) {
        return new Vector2(this.x - other.x, this.y - other.y);
    }
    
    multiply(scalar) {
        return new Vector2(this.x * scalar, this.y * scalar);
    }
    
    magnitude() {
        return Math.sqrt(this.x * this.x + this.y * this.y);
    }
    
    normalize() {
        const mag = this.magnitude();
        if (mag === 0) return new Vector2(0, 0);
        return new Vector2(this.x / mag, this.y / mag);
    }
    
    distance(other) {
        return this.subtract(other).magnitude();
    }
}

class Rectangle {
    constructor(x, y, width, height) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }
    
    get left() { return this.x; }
    get right() { return this.x + this.width; }
    get top() { return this.y; }
    get bottom() { return this.y + this.height; }
    get centerX() { return this.x + this.width / 2; }
    get centerY() { return this.y + this.height / 2; }
    
    intersects(other) {
        return !(this.right < other.left || 
                this.left > other.right || 
                this.bottom < other.top || 
                this.top > other.bottom);
    }
    
    contains(point) {
        return point.x >= this.left && point.x <= this.right &&
               point.y >= this.top && point.y <= this.bottom;
    }
}

// Animation helper
class Animation {
    constructor(frames, frameRate = 10) {
        this.frames = frames;
        this.frameRate = frameRate;
        this.currentFrame = 0;
        this.frameTime = 1000 / frameRate;
        this.lastFrameTime = 0;
        this.loop = true;
        this.playing = true;
    }
    
    update(deltaTime) {
        if (!this.playing) return;
        
        this.lastFrameTime += deltaTime;
        if (this.lastFrameTime >= this.frameTime) {
            this.currentFrame++;
            if (this.currentFrame >= this.frames.length) {
                if (this.loop) {
                    this.currentFrame = 0;
                } else {
                    this.currentFrame = this.frames.length - 1;
                    this.playing = false;
                }
            }
            this.lastFrameTime = 0;
        }
    }
    
    getCurrentFrame() {
        return this.frames[this.currentFrame];
    }
    
    reset() {
        this.currentFrame = 0;
        this.lastFrameTime = 0;
        this.playing = true;
    }
}

// Utility functions
const Utils = {
    clamp: (value, min, max) => Math.max(min, Math.min(max, value)),
    
    lerp: (start, end, factor) => start + (end - start) * factor,
    
    randomRange: (min, max) => Math.random() * (max - min) + min,
    
    randomInt: (min, max) => Math.floor(Math.random() * (max - min + 1)) + min,
    
    degToRad: (degrees) => degrees * Math.PI / 180,
    
    radToDeg: (radians) => radians * 180 / Math.PI,
    
    // Check if two circles collide
    circleCollision: (pos1, radius1, pos2, radius2) => {
        const distance = pos1.distance(pos2);
        return distance < radius1 + radius2;
    },
    
    // Get angle between two points
    angleBetween: (from, to) => {
        return Math.atan2(to.y - from.y, to.x - from.x);
    },
    
    // Create a simple particle effect
    createParticles: (position, count, color = '#ff6b35') => {
        const particles = [];
        for (let i = 0; i < count; i++) {
            particles.push({
                x: position.x,
                y: position.y,
                vx: Utils.randomRange(-3, 3),
                vy: Utils.randomRange(-5, -1),
                life: 1.0,
                decay: Utils.randomRange(0.02, 0.05),
                color: color,
                size: Utils.randomRange(2, 6)
            });
        }
        return particles;
    },
    
    // Update and draw particles
    updateParticles: (particles, deltaTime, ctx) => {
        for (let i = particles.length - 1; i >= 0; i--) {
            const particle = particles[i];
            
            // Update position
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.vy += 0.2; // gravity
            
            // Update life
            particle.life -= particle.decay;
            
            // Draw particle
            ctx.save();
            ctx.globalAlpha = particle.life;
            ctx.fillStyle = particle.color;
            ctx.fillRect(particle.x - particle.size/2, particle.y - particle.size/2, 
                        particle.size, particle.size);
            ctx.restore();
            
            // Remove dead particles
            if (particle.life <= 0) {
                particles.splice(i, 1);
            }
        }
    }
};

// Color constants for MHA theme
const Colors = {
    HERO_BLUE: '#2196F3',
    HERO_RED: '#ff6b35',
    HERO_GOLD: '#ffd700',
    VILLAIN_PURPLE: '#9C27B0',
    QUIRK_GREEN: '#4CAF50',
    UI_DARK: 'rgba(0, 0, 0, 0.8)',
    UI_LIGHT: 'rgba(255, 255, 255, 0.9)'
};
