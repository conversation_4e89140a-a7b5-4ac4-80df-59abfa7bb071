// Sprite and animation system for the MHA game

class Sprite {
    constructor(x, y, width, height, color = '#ff6b35') {
        this.position = new Vector2(x, y);
        this.width = width;
        this.height = height;
        this.color = color;
        this.visible = true;
        this.rotation = 0;
        this.scale = new Vector2(1, 1);
        this.origin = new Vector2(0.5, 0.5); // Center origin
    }
    
    draw(ctx) {
        if (!this.visible) return;
        
        ctx.save();
        
        // Apply transformations
        const centerX = this.position.x + this.width * this.origin.x;
        const centerY = this.position.y + this.height * this.origin.y;
        
        ctx.translate(centerX, centerY);
        ctx.rotate(this.rotation);
        ctx.scale(this.scale.x, this.scale.y);
        
        // Draw the sprite (simple colored rectangle for now)
        ctx.fillStyle = this.color;
        ctx.fillRect(
            -this.width * this.origin.x,
            -this.height * this.origin.y,
            this.width,
            this.height
        );
        
        ctx.restore();
    }
    
    getBounds() {
        return new Rectangle(this.position.x, this.position.y, this.width, this.height);
    }
    
    getCenter() {
        return new Vector2(
            this.position.x + this.width * this.origin.x,
            this.position.y + this.height * this.origin.y
        );
    }
}

// Animated sprite that can cycle through different states
class AnimatedSprite extends Sprite {
    constructor(x, y, width, height) {
        super(x, y, width, height);
        this.animations = {};
        this.currentAnimation = null;
        this.animationName = '';
        this.flipX = false;
        this.flipY = false;
    }
    
    addAnimation(name, colors, frameRate = 8) {
        this.animations[name] = {
            colors: colors,
            currentFrame: 0,
            frameTime: 1000 / frameRate,
            lastFrameTime: 0,
            loop: true
        };
    }
    
    playAnimation(name, loop = true) {
        if (this.animationName === name) return;
        
        if (this.animations[name]) {
            this.animationName = name;
            this.currentAnimation = this.animations[name];
            this.currentAnimation.currentFrame = 0;
            this.currentAnimation.lastFrameTime = 0;
            this.currentAnimation.loop = loop;
        }
    }
    
    update(deltaTime) {
        if (!this.currentAnimation) return;
        
        this.currentAnimation.lastFrameTime += deltaTime;
        
        if (this.currentAnimation.lastFrameTime >= this.currentAnimation.frameTime) {
            this.currentAnimation.currentFrame++;
            
            if (this.currentAnimation.currentFrame >= this.currentAnimation.colors.length) {
                if (this.currentAnimation.loop) {
                    this.currentAnimation.currentFrame = 0;
                } else {
                    this.currentAnimation.currentFrame = this.currentAnimation.colors.length - 1;
                }
            }
            
            this.currentAnimation.lastFrameTime = 0;
        }
        
        // Update sprite color based on current frame
        if (this.currentAnimation.colors.length > 0) {
            this.color = this.currentAnimation.colors[this.currentAnimation.currentFrame];
        }
    }
    
    draw(ctx) {
        if (!this.visible) return;
        
        ctx.save();
        
        // Apply transformations
        const centerX = this.position.x + this.width * this.origin.x;
        const centerY = this.position.y + this.height * this.origin.y;
        
        ctx.translate(centerX, centerY);
        ctx.rotate(this.rotation);
        
        // Apply flipping
        let scaleX = this.scale.x * (this.flipX ? -1 : 1);
        let scaleY = this.scale.y * (this.flipY ? -1 : 1);
        ctx.scale(scaleX, scaleY);
        
        // Draw the sprite
        ctx.fillStyle = this.color;
        ctx.fillRect(
            -this.width * this.origin.x,
            -this.height * this.origin.y,
            this.width,
            this.height
        );
        
        // Add some basic character details
        this.drawCharacterDetails(ctx);
        
        ctx.restore();
    }
    
    drawCharacterDetails(ctx) {
        // Draw simple character features (eyes, etc.)
        ctx.fillStyle = '#ffffff';
        
        // Eyes
        const eyeSize = 4;
        const eyeY = -this.height * 0.3;
        ctx.fillRect(-this.width * 0.2 - eyeSize/2, eyeY, eyeSize, eyeSize);
        ctx.fillRect(this.width * 0.2 - eyeSize/2, eyeY, eyeSize, eyeSize);
        
        // Eye pupils
        ctx.fillStyle = '#000000';
        const pupilSize = 2;
        ctx.fillRect(-this.width * 0.2 - pupilSize/2, eyeY + 1, pupilSize, pupilSize);
        ctx.fillRect(this.width * 0.2 - pupilSize/2, eyeY + 1, pupilSize, pupilSize);
    }
}

// Effect sprite for visual effects like explosions, quirk effects, etc.
class EffectSprite {
    constructor(x, y, effectType = 'explosion') {
        this.position = new Vector2(x, y);
        this.effectType = effectType;
        this.life = 1.0;
        this.maxLife = 1.0;
        this.size = 20;
        this.maxSize = 40;
        this.particles = [];
        this.active = true;
        
        this.initializeEffect();
    }
    
    initializeEffect() {
        switch (this.effectType) {
            case 'explosion':
                this.particles = Utils.createParticles(this.position, 15, '#ff6b35');
                break;
            case 'quirk':
                this.particles = Utils.createParticles(this.position, 10, '#2196F3');
                break;
            case 'hit':
                this.particles = Utils.createParticles(this.position, 8, '#ffff00');
                break;
        }
    }
    
    update(deltaTime) {
        if (!this.active) return;
        
        this.life -= deltaTime / 1000;
        
        if (this.life <= 0) {
            this.active = false;
            return;
        }
        
        // Update size based on life
        const lifeRatio = this.life / this.maxLife;
        this.size = this.maxSize * lifeRatio;
        
        // Update particles
        Utils.updateParticles(this.particles, deltaTime, null);
    }
    
    draw(ctx) {
        if (!this.active) return;
        
        // Draw main effect
        ctx.save();
        ctx.globalAlpha = this.life / this.maxLife;
        
        switch (this.effectType) {
            case 'explosion':
                this.drawExplosion(ctx);
                break;
            case 'quirk':
                this.drawQuirkEffect(ctx);
                break;
            case 'hit':
                this.drawHitEffect(ctx);
                break;
        }
        
        ctx.restore();
        
        // Draw particles
        Utils.updateParticles(this.particles, 16, ctx);
    }
    
    drawExplosion(ctx) {
        const gradient = ctx.createRadialGradient(
            this.position.x, this.position.y, 0,
            this.position.x, this.position.y, this.size
        );
        gradient.addColorStop(0, '#ffff00');
        gradient.addColorStop(0.5, '#ff6b35');
        gradient.addColorStop(1, 'rgba(255, 107, 53, 0)');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(this.position.x, this.position.y, this.size, 0, Math.PI * 2);
        ctx.fill();
    }
    
    drawQuirkEffect(ctx) {
        const gradient = ctx.createRadialGradient(
            this.position.x, this.position.y, 0,
            this.position.x, this.position.y, this.size
        );
        gradient.addColorStop(0, '#03DAC6');
        gradient.addColorStop(0.5, '#2196F3');
        gradient.addColorStop(1, 'rgba(33, 150, 243, 0)');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(this.position.x, this.position.y, this.size, 0, Math.PI * 2);
        ctx.fill();
    }
    
    drawHitEffect(ctx) {
        ctx.strokeStyle = '#ffff00';
        ctx.lineWidth = 3;
        ctx.beginPath();
        
        // Draw impact lines
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const startX = this.position.x + Math.cos(angle) * (this.size * 0.3);
            const startY = this.position.y + Math.sin(angle) * (this.size * 0.3);
            const endX = this.position.x + Math.cos(angle) * this.size;
            const endY = this.position.y + Math.sin(angle) * this.size;
            
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
        }
        
        ctx.stroke();
    }
}
