// Audio system for the MHA game using Web Audio API

class AudioManager {
    constructor() {
        this.audioContext = null;
        this.sounds = {};
        this.musicVolume = 0.3;
        this.sfxVolume = 0.5;
        this.enabled = true;
        
        this.initializeAudio();
        this.createSounds();
    }
    
    initializeAudio() {
        try {
            // Create audio context
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Handle audio context state
            if (this.audioContext.state === 'suspended') {
                // Resume on user interaction
                document.addEventListener('click', () => {
                    if (this.audioContext.state === 'suspended') {
                        this.audioContext.resume();
                    }
                }, { once: true });
            }
        } catch (error) {
            console.warn('Web Audio API not supported:', error);
            this.enabled = false;
        }
    }
    
    createSounds() {
        if (!this.enabled) return;
        
        // Create procedural sound effects
        this.sounds = {
            jump: this.createJumpSound(),
            attack: this.createAttackSound(),
            quirk: this.createQuirkSound(),
            hit: this.createHitSound(),
            explosion: this.createExplosionSound(),
            levelUp: this.createLevelUpSound(),
            enemyHit: this.createEnemyHitSound(),
            gameOver: this.createGameOverSound()
        };
    }
    
    createOscillator(frequency, type = 'sine', duration = 0.2) {
        if (!this.audioContext) return null;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
        oscillator.type = type;
        
        // Envelope
        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(this.sfxVolume, this.audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
        
        return { oscillator, gainNode, duration };
    }
    
    createJumpSound() {
        return () => {
            const sound = this.createOscillator(200, 'sine', 0.3);
            if (sound) {
                sound.oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.1);
                sound.oscillator.start();
                sound.oscillator.stop(this.audioContext.currentTime + sound.duration);
            }
        };
    }
    
    createAttackSound() {
        return () => {
            const sound = this.createOscillator(150, 'sawtooth', 0.15);
            if (sound) {
                sound.oscillator.frequency.exponentialRampToValueAtTime(100, this.audioContext.currentTime + 0.1);
                sound.oscillator.start();
                sound.oscillator.stop(this.audioContext.currentTime + sound.duration);
            }
        };
    }
    
    createQuirkSound() {
        return () => {
            // Create multiple oscillators for a richer sound
            for (let i = 0; i < 3; i++) {
                const frequency = 300 + i * 100;
                const sound = this.createOscillator(frequency, 'square', 0.4);
                if (sound) {
                    sound.oscillator.frequency.exponentialRampToValueAtTime(frequency * 1.5, this.audioContext.currentTime + 0.2);
                    sound.oscillator.start(this.audioContext.currentTime + i * 0.05);
                    sound.oscillator.stop(this.audioContext.currentTime + sound.duration);
                }
            }
        };
    }
    
    createHitSound() {
        return () => {
            const sound = this.createOscillator(800, 'square', 0.1);
            if (sound) {
                sound.oscillator.frequency.exponentialRampToValueAtTime(200, this.audioContext.currentTime + 0.05);
                sound.oscillator.start();
                sound.oscillator.stop(this.audioContext.currentTime + sound.duration);
            }
        };
    }
    
    createExplosionSound() {
        return () => {
            // White noise for explosion
            if (!this.audioContext) return;
            
            const bufferSize = this.audioContext.sampleRate * 0.3;
            const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
            const data = buffer.getChannelData(0);
            
            for (let i = 0; i < bufferSize; i++) {
                data[i] = (Math.random() * 2 - 1) * Math.pow(1 - i / bufferSize, 2);
            }
            
            const source = this.audioContext.createBufferSource();
            const gainNode = this.audioContext.createGain();
            
            source.buffer = buffer;
            source.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            gainNode.gain.setValueAtTime(this.sfxVolume, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.3);
            
            source.start();
        };
    }
    
    createLevelUpSound() {
        return () => {
            // Ascending arpeggio
            const notes = [261.63, 329.63, 392.00, 523.25]; // C, E, G, C
            notes.forEach((frequency, index) => {
                const sound = this.createOscillator(frequency, 'sine', 0.3);
                if (sound) {
                    sound.oscillator.start(this.audioContext.currentTime + index * 0.1);
                    sound.oscillator.stop(this.audioContext.currentTime + index * 0.1 + sound.duration);
                }
            });
        };
    }
    
    createEnemyHitSound() {
        return () => {
            const sound = this.createOscillator(120, 'sawtooth', 0.2);
            if (sound) {
                sound.oscillator.frequency.exponentialRampToValueAtTime(80, this.audioContext.currentTime + 0.1);
                sound.oscillator.start();
                sound.oscillator.stop(this.audioContext.currentTime + sound.duration);
            }
        };
    }
    
    createGameOverSound() {
        return () => {
            // Descending notes
            const notes = [523.25, 392.00, 329.63, 261.63]; // C, G, E, C
            notes.forEach((frequency, index) => {
                const sound = this.createOscillator(frequency, 'sine', 0.5);
                if (sound) {
                    sound.oscillator.start(this.audioContext.currentTime + index * 0.3);
                    sound.oscillator.stop(this.audioContext.currentTime + index * 0.3 + sound.duration);
                }
            });
        };
    }
    
    playSound(soundName) {
        if (!this.enabled || !this.sounds[soundName]) return;
        
        try {
            this.sounds[soundName]();
        } catch (error) {
            console.warn('Error playing sound:', soundName, error);
        }
    }
    
    setVolume(type, volume) {
        volume = Math.max(0, Math.min(1, volume));
        
        if (type === 'sfx') {
            this.sfxVolume = volume;
        } else if (type === 'music') {
            this.musicVolume = volume;
        }
    }
    
    toggle() {
        this.enabled = !this.enabled;
        return this.enabled;
    }
    
    // Background music using oscillators
    startBackgroundMusic() {
        if (!this.enabled || !this.audioContext) return;
        
        this.stopBackgroundMusic();
        
        // Simple background music loop
        const playMusicLoop = () => {
            if (!this.enabled) return;
            
            // Hero theme melody (simplified)
            const melody = [
                { freq: 261.63, duration: 0.5 }, // C
                { freq: 329.63, duration: 0.5 }, // E
                { freq: 392.00, duration: 0.5 }, // G
                { freq: 523.25, duration: 1.0 }, // C
                { freq: 392.00, duration: 0.5 }, // G
                { freq: 329.63, duration: 0.5 }, // E
                { freq: 261.63, duration: 1.0 }  // C
            ];
            
            let currentTime = this.audioContext.currentTime;
            
            melody.forEach(note => {
                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(this.audioContext.destination);
                
                oscillator.frequency.setValueAtTime(note.freq, currentTime);
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0, currentTime);
                gainNode.gain.linearRampToValueAtTime(this.musicVolume * 0.1, currentTime + 0.01);
                gainNode.gain.linearRampToValueAtTime(0, currentTime + note.duration);
                
                oscillator.start(currentTime);
                oscillator.stop(currentTime + note.duration);
                
                currentTime += note.duration;
            });
            
            // Schedule next loop
            this.musicTimeout = setTimeout(playMusicLoop, melody.reduce((sum, note) => sum + note.duration, 0) * 1000 + 2000);
        };
        
        playMusicLoop();
    }
    
    stopBackgroundMusic() {
        if (this.musicTimeout) {
            clearTimeout(this.musicTimeout);
            this.musicTimeout = null;
        }
    }
}

// Create global audio manager
window.audioManager = new AudioManager();
