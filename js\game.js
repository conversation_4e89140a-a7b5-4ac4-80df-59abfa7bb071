// Main game class for the MHA game

class Game {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.width = canvas.width;
        this.height = canvas.height;
        
        // Game state
        this.state = 'menu'; // menu, playing, paused, gameOver
        this.lastTime = 0;
        this.deltaTime = 0;
        
        // Game objects
        this.player = null;
        this.level = null;
        this.enemySpawner = null;
        this.inputManager = new InputManager();
        this.touchInputManager = new TouchInputManager(canvas);
        
        // Visual effects
        this.effects = [];
        this.particles = [];
        this.screenShake = { intensity: 0, duration: 0 };
        this.camera = { x: 0, y: 0 };
        
        // UI elements
        this.messages = [];
        this.gameTime = 0;
        
        this.initialize();
    }
    
    initialize() {
        // Create level
        this.level = new Level(this.width, this.height);
        
        // Create player
        this.player = new Player(100, this.level.groundY - 60);
        
        // Create enemy spawner
        this.enemySpawner = new EnemySpawner(this.level);
        
        // Setup UI event listeners
        this.setupUIEvents();
    }
    
    setupUIEvents() {
        // Main menu buttons
        document.getElementById('startBtn').addEventListener('click', () => {
            this.startGame();
        });
        
        document.getElementById('resumeBtn').addEventListener('click', () => {
            this.resume();
        });
        
        document.getElementById('restartBtn').addEventListener('click', () => {
            this.restart();
        });
        
        document.getElementById('mainMenuBtn').addEventListener('click', () => {
            this.showMainMenu();
        });
        
        document.getElementById('playAgainBtn').addEventListener('click', () => {
            this.restart();
        });
        
        document.getElementById('backToMenuBtn').addEventListener('click', () => {
            this.showMainMenu();
        });

        // Sound toggle button
        document.getElementById('soundToggleBtn').addEventListener('click', () => {
            if (window.audioManager) {
                const enabled = window.audioManager.toggle();
                const btn = document.getElementById('soundToggleBtn');
                btn.textContent = enabled ? '🔊 Sound: ON' : '🔇 Sound: OFF';
            }
        });
    }
    
    startGame() {
        this.state = 'playing';
        this.gameTime = 0;
        this.showScreen('gameUI');
        this.player.reset();
        this.enemySpawner.reset();
        this.effects = [];
        this.particles = [];
        this.messages = [];

        // Start background music
        if (window.audioManager) {
            window.audioManager.startBackgroundMusic();
        }
    }
    
    pause() {
        if (this.state === 'playing') {
            this.state = 'paused';
            this.showScreen('pauseMenu');
        }
    }
    
    resume() {
        if (this.state === 'paused') {
            this.state = 'playing';
            this.showScreen('gameUI');
        }
    }
    
    restart() {
        this.startGame();
    }
    
    showMainMenu() {
        this.state = 'menu';
        this.showScreen('mainMenu');

        // Stop background music
        if (window.audioManager) {
            window.audioManager.stopBackgroundMusic();
        }
    }
    
    gameOver() {
        this.state = 'gameOver';
        document.getElementById('finalScoreValue').textContent = this.player.score;
        this.showScreen('gameOverScreen');

        // Stop background music and play game over sound
        if (window.audioManager) {
            window.audioManager.stopBackgroundMusic();
            window.audioManager.playSound('gameOver');
        }
    }
    
    showScreen(screenId) {
        // Hide all screens
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        
        // Show target screen
        document.getElementById(screenId).classList.add('active');
    }
    
    update(currentTime) {
        this.deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // Limit delta time to prevent large jumps
        this.deltaTime = Math.min(this.deltaTime, 50);
        
        if (this.state === 'playing') {
            this.updateGame();
        }
        
        // Handle pause input
        if (this.inputManager.isKeyPressed('pause')) {
            if (this.state === 'playing') {
                this.pause();
            } else if (this.state === 'paused') {
                this.resume();
            }
        }
        
        // Update input manager
        this.inputManager.update();
    }
    
    updateGame() {
        this.gameTime += this.deltaTime;
        
        // Update game objects
        this.player.update(this.deltaTime, this.level, this.inputManager);
        this.enemySpawner.update(this.deltaTime, this.player);
        this.level.update(this.deltaTime, this.camera.x);
        
        // Update effects
        this.updateEffects();
        
        // Update camera
        this.updateCamera();
        
        // Update screen shake
        this.updateScreenShake();
        
        // Update UI
        this.updateUI();
        
        // Check game over condition
        if (!this.player.alive) {
            this.gameOver();
        }
    }
    
    updateEffects() {
        // Update visual effects
        for (let i = this.effects.length - 1; i >= 0; i--) {
            this.effects[i].update(this.deltaTime);
            if (!this.effects[i].active) {
                this.effects.splice(i, 1);
            }
        }
        
        // Update particles
        Utils.updateParticles(this.particles, this.deltaTime, this.ctx);
        
        // Update messages
        for (let i = this.messages.length - 1; i >= 0; i--) {
            this.messages[i].life -= this.deltaTime;
            if (this.messages[i].life <= 0) {
                this.messages.splice(i, 1);
            }
        }
    }
    
    updateCamera() {
        // Simple camera following player
        const targetX = this.player.position.x - this.width / 2;
        this.camera.x = Utils.lerp(this.camera.x, targetX, 0.1);
        
        // Keep camera within level bounds
        this.camera.x = Utils.clamp(this.camera.x, 0, this.level.width - this.width);
    }
    
    updateScreenShake() {
        if (this.screenShake.duration > 0) {
            this.screenShake.duration -= this.deltaTime;
            if (this.screenShake.duration <= 0) {
                this.screenShake.intensity = 0;
            }
        }
    }
    
    updateUI() {
        // Update health bar
        const healthPercent = (this.player.health / this.player.maxHealth) * 100;
        document.getElementById('healthFill').style.width = healthPercent + '%';
        
        // Update quirk bar
        const quirkPercent = (this.player.quirkEnergy / this.player.maxQuirkEnergy) * 100;
        document.getElementById('quirkFill').style.width = quirkPercent + '%';
        
        // Update score
        document.getElementById('scoreValue').textContent = this.player.score;
    }
    
    draw() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        // Apply camera transform
        this.ctx.save();
        
        // Apply screen shake
        if (this.screenShake.intensity > 0) {
            const shakeX = (Math.random() - 0.5) * this.screenShake.intensity;
            const shakeY = (Math.random() - 0.5) * this.screenShake.intensity;
            this.ctx.translate(shakeX, shakeY);
        }
        
        this.ctx.translate(-this.camera.x, -this.camera.y);
        
        if (this.state === 'playing' || this.state === 'paused') {
            // Draw level
            this.level.draw(this.ctx, this.camera.x);
            
            // Draw enemies
            this.enemySpawner.draw(this.ctx);
            
            // Draw player
            this.player.draw(this.ctx);
            
            // Draw effects
            this.effects.forEach(effect => {
                effect.draw(this.ctx);
            });
        }
        
        this.ctx.restore();
        
        // Draw UI elements that don't move with camera
        this.drawUI();
        
        // Draw touch controls on mobile
        this.touchInputManager.draw(this.ctx);
    }
    
    drawUI() {
        // Draw messages
        this.messages.forEach((message, index) => {
            const alpha = Math.min(1, message.life / 1000);
            this.ctx.save();
            this.ctx.globalAlpha = alpha;
            this.ctx.font = 'bold 24px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillStyle = '#ffff00';
            this.ctx.strokeStyle = '#000000';
            this.ctx.lineWidth = 2;
            
            const y = this.height / 2 - 50 + index * 30;
            this.ctx.strokeText(message.text, this.width / 2, y);
            this.ctx.fillText(message.text, this.width / 2, y);
            this.ctx.restore();
        });
        
        // Draw wave information
        if (this.state === 'playing') {
            this.ctx.save();
            this.ctx.font = '16px Arial';
            this.ctx.textAlign = 'right';
            this.ctx.fillStyle = '#ffffff';
            this.ctx.strokeStyle = '#000000';
            this.ctx.lineWidth = 1;
            
            const waveText = `Wave: ${this.enemySpawner.waveNumber}`;
            this.ctx.strokeText(waveText, this.width - 20, 30);
            this.ctx.fillText(waveText, this.width - 20, 30);
            
            const enemiesText = `Enemies: ${this.enemySpawner.enemies.length}`;
            this.ctx.strokeText(enemiesText, this.width - 20, 50);
            this.ctx.fillText(enemiesText, this.width - 20, 50);
            
            this.ctx.restore();
        }
    }
    
    // Game event handlers
    handlePlayerAttack(attackArea, damage, isQuirk) {
        const enemies = this.enemySpawner.getEnemies();
        let hitCount = 0;
        
        enemies.forEach(enemy => {
            if (enemy.alive && attackArea.intersects(enemy.getBounds())) {
                const knockback = new Vector2(
                    this.player.facing * (isQuirk ? 200 : 100),
                    isQuirk ? -100 : -50
                );
                
                if (enemy.takeDamage(damage, knockback)) {
                    hitCount++;
                }
            }
        });
        
        return hitCount;
    }
    
    addEffect(effect) {
        this.effects.push(effect);
    }
    
    addScreenShake(intensity, duration) {
        this.screenShake.intensity = intensity;
        this.screenShake.duration = duration;
    }
    
    showMessage(text, duration = 2000) {
        this.messages.push({
            text: text,
            life: duration
        });
    }
    
    // Game loop
    run() {
        const gameLoop = (currentTime) => {
            this.update(currentTime);
            this.draw();
            requestAnimationFrame(gameLoop);
        };
        
        requestAnimationFrame(gameLoop);
    }
}

// Make game globally accessible
window.Game = Game;
