// Level and environment system for the MHA game

class Level {
    constructor(width = 1024, height = 768) {
        this.width = width;
        this.height = height;
        this.groundY = height - 100;
        this.platforms = [];
        this.background = null;
        this.theme = 'training_ground';
        this.parallaxLayers = [];
        
        this.setupLevel();
    }
    
    setupLevel() {
        // Create ground platforms
        this.platforms.push(new Platform(0, this.groundY, this.width, 100, '#4CAF50'));
        
        // Add some floating platforms
        this.platforms.push(new Platform(200, this.groundY - 120, 150, 20, '#8BC34A'));
        this.platforms.push(new Platform(450, this.groundY - 200, 120, 20, '#8BC34A'));
        this.platforms.push(new Platform(650, this.groundY - 120, 150, 20, '#8BC34A'));
        this.platforms.push(new Platform(100, this.groundY - 280, 100, 20, '#8BC34A'));
        this.platforms.push(new Platform(750, this.groundY - 250, 120, 20, '#8BC34A'));
        
        // Setup parallax background layers
        this.setupParallax();
    }
    
    setupParallax() {
        // Sky layer (static)
        this.parallaxLayers.push({
            color: 'linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%)',
            speed: 0,
            offset: 0
        });
        
        // Distant mountains
        this.parallaxLayers.push({
            elements: this.generateMountains(0.1),
            speed: 0.1,
            offset: 0,
            color: '#9E9E9E'
        });
        
        // Mid-distance buildings (UA High School inspired)
        this.parallaxLayers.push({
            elements: this.generateBuildings(0.3),
            speed: 0.3,
            offset: 0,
            color: '#607D8B'
        });
        
        // Foreground trees
        this.parallaxLayers.push({
            elements: this.generateTrees(0.6),
            speed: 0.6,
            offset: 0,
            color: '#4CAF50'
        });
    }
    
    generateMountains(parallaxSpeed) {
        const mountains = [];
        for (let x = 0; x < this.width * 2; x += 100) {
            mountains.push({
                x: x,
                y: this.groundY - Utils.randomRange(100, 200),
                width: Utils.randomRange(80, 150),
                height: Utils.randomRange(100, 200)
            });
        }
        return mountains;
    }
    
    generateBuildings(parallaxSpeed) {
        const buildings = [];
        for (let x = 0; x < this.width * 2; x += 120) {
            buildings.push({
                x: x,
                y: this.groundY - Utils.randomRange(150, 300),
                width: Utils.randomRange(60, 100),
                height: Utils.randomRange(150, 300),
                windows: Math.floor(Utils.randomRange(3, 8))
            });
        }
        return buildings;
    }
    
    generateTrees(parallaxSpeed) {
        const trees = [];
        for (let x = 0; x < this.width * 2; x += 80) {
            if (Math.random() > 0.3) { // Not every position has a tree
                trees.push({
                    x: x,
                    y: this.groundY - Utils.randomRange(60, 120),
                    width: Utils.randomRange(20, 40),
                    height: Utils.randomRange(60, 120)
                });
            }
        }
        return trees;
    }
    
    update(deltaTime, cameraX = 0) {
        // Update parallax offsets based on camera movement
        this.parallaxLayers.forEach(layer => {
            if (layer.speed > 0) {
                layer.offset = cameraX * layer.speed;
            }
        });
    }
    
    draw(ctx, cameraX = 0) {
        // Draw background gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, this.height);
        gradient.addColorStop(0, '#87CEEB');
        gradient.addColorStop(1, '#98FB98');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, this.width, this.height);
        
        // Draw parallax layers
        this.drawParallaxLayers(ctx, cameraX);
        
        // Draw platforms
        this.platforms.forEach(platform => {
            platform.draw(ctx);
        });
        
        // Draw level decorations
        this.drawDecorations(ctx);
    }
    
    drawParallaxLayers(ctx, cameraX) {
        this.parallaxLayers.forEach((layer, index) => {
            if (index === 0) return; // Skip sky layer (already drawn as gradient)
            
            ctx.save();
            ctx.fillStyle = layer.color;
            
            if (layer.elements) {
                layer.elements.forEach(element => {
                    const drawX = element.x - layer.offset;
                    
                    // Only draw if element is visible
                    if (drawX + element.width > -cameraX && drawX < -cameraX + this.width) {
                        switch (index) {
                            case 1: // Mountains
                                this.drawMountain(ctx, element, drawX);
                                break;
                            case 2: // Buildings
                                this.drawBuilding(ctx, element, drawX);
                                break;
                            case 3: // Trees
                                this.drawTree(ctx, element, drawX);
                                break;
                        }
                    }
                });
            }
            
            ctx.restore();
        });
    }
    
    drawMountain(ctx, mountain, x) {
        ctx.fillStyle = '#9E9E9E';
        ctx.beginPath();
        ctx.moveTo(x, this.groundY);
        ctx.lineTo(x + mountain.width / 2, mountain.y);
        ctx.lineTo(x + mountain.width, this.groundY);
        ctx.closePath();
        ctx.fill();
        
        // Snow cap
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.moveTo(x + mountain.width * 0.3, mountain.y + mountain.height * 0.3);
        ctx.lineTo(x + mountain.width / 2, mountain.y);
        ctx.lineTo(x + mountain.width * 0.7, mountain.y + mountain.height * 0.3);
        ctx.closePath();
        ctx.fill();
    }
    
    drawBuilding(ctx, building, x) {
        // Main building
        ctx.fillStyle = '#607D8B';
        ctx.fillRect(x, building.y, building.width, building.height);
        
        // Windows
        ctx.fillStyle = '#FFF59D';
        const windowSize = 8;
        const windowSpacing = 15;
        
        for (let row = 0; row < Math.floor(building.height / windowSpacing); row++) {
            for (let col = 0; col < Math.floor(building.width / windowSpacing); col++) {
                if (Math.random() > 0.3) { // Some windows are lit
                    const windowX = x + col * windowSpacing + 5;
                    const windowY = building.y + row * windowSpacing + 5;
                    ctx.fillRect(windowX, windowY, windowSize, windowSize);
                }
            }
        }
        
        // Rooftop
        ctx.fillStyle = '#455A64';
        ctx.fillRect(x - 5, building.y - 10, building.width + 10, 10);
    }
    
    drawTree(ctx, tree, x) {
        // Trunk
        ctx.fillStyle = '#5D4037';
        const trunkWidth = tree.width * 0.3;
        ctx.fillRect(x + tree.width/2 - trunkWidth/2, tree.y + tree.height * 0.6, 
                    trunkWidth, tree.height * 0.4);
        
        // Foliage
        ctx.fillStyle = '#4CAF50';
        ctx.beginPath();
        ctx.arc(x + tree.width/2, tree.y + tree.height * 0.3, tree.width/2, 0, Math.PI * 2);
        ctx.fill();
        
        // Highlight
        ctx.fillStyle = '#66BB6A';
        ctx.beginPath();
        ctx.arc(x + tree.width/2 - tree.width * 0.2, tree.y + tree.height * 0.2, 
               tree.width * 0.3, 0, Math.PI * 2);
        ctx.fill();
    }
    
    drawDecorations(ctx) {
        // Draw some training equipment scattered around
        this.drawTrainingEquipment(ctx);
        
        // Draw UA High School logo/sign
        this.drawUASign(ctx);
    }
    
    drawTrainingEquipment(ctx) {
        // Training dummies
        const dummyPositions = [150, 400, 700, 900];
        dummyPositions.forEach(x => {
            if (x < this.width - 50) {
                ctx.fillStyle = '#FF5722';
                ctx.fillRect(x, this.groundY - 40, 20, 40);
                
                // Dummy head
                ctx.fillStyle = '#FFAB91';
                ctx.beginPath();
                ctx.arc(x + 10, this.groundY - 50, 8, 0, Math.PI * 2);
                ctx.fill();
            }
        });
        
        // Training obstacles
        ctx.fillStyle = '#795548';
        ctx.fillRect(300, this.groundY - 30, 60, 30); // Hurdle
        ctx.fillRect(550, this.groundY - 25, 40, 25); // Block
    }
    
    drawUASign(ctx) {
        const signX = 50;
        const signY = this.groundY - 200;
        
        // Sign post
        ctx.fillStyle = '#795548';
        ctx.fillRect(signX + 35, signY + 40, 10, 60);
        
        // Sign board
        ctx.fillStyle = '#2196F3';
        ctx.fillRect(signX, signY, 80, 40);
        
        // UA text
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('U.A.', signX + 40, signY + 15);
        ctx.fillText('HIGH', signX + 40, signY + 30);
    }
    
    checkCollisions(character) {
        const characterBounds = character.getBounds();
        
        for (let platform of this.platforms) {
            if (platform.checkCollision(characterBounds, character.velocity)) {
                return true;
            }
        }
        
        return false;
    }
    
    getPlatforms() {
        return this.platforms;
    }
}

class Platform {
    constructor(x, y, width, height, color = '#4CAF50') {
        this.bounds = new Rectangle(x, y, width, height);
        this.color = color;
        this.type = 'solid'; // solid, jumpthrough, etc.
    }
    
    draw(ctx) {
        ctx.fillStyle = this.color;
        ctx.fillRect(this.bounds.x, this.bounds.y, this.bounds.width, this.bounds.height);
        
        // Add some texture/detail
        if (this.bounds.height > 20) {
            // Grass on top
            ctx.fillStyle = '#66BB6A';
            ctx.fillRect(this.bounds.x, this.bounds.y, this.bounds.width, 5);
            
            // Dirt texture
            ctx.fillStyle = '#8D6E63';
            for (let i = 0; i < this.bounds.width; i += 10) {
                for (let j = 5; j < this.bounds.height; j += 10) {
                    if (Math.random() > 0.7) {
                        ctx.fillRect(this.bounds.x + i, this.bounds.y + j, 2, 2);
                    }
                }
            }
        } else {
            // Simple platform highlight
            ctx.fillStyle = '#A5D6A7';
            ctx.fillRect(this.bounds.x, this.bounds.y, this.bounds.width, 2);
        }
    }
    
    checkCollision(characterBounds, velocity) {
        if (!this.bounds.intersects(characterBounds)) {
            return false;
        }
        
        // Simple collision response for now
        // In a more complex system, you'd handle different collision sides
        return true;
    }
}
