# My Hero Academia - 2D Game

🦸‍♂️ **Plus Ultra!** A 2D action game inspired by the popular anime My Hero Academia.

## 🎮 Game Features

- **Hero Training Simulator**: Play as a hero-in-training and fight against villains
- **Quirk System**: Use special abilities inspired by the anime
- **Wave-based Combat**: Face increasingly difficult waves of enemies
- **Level Progression**: Gain experience and level up to become stronger
- **Combo System**: Chain attacks for higher scores
- **Multiple Enemy Types**: Face different villain types with unique behaviors
- **Responsive Controls**: Smooth movement and combat mechanics
- **Visual Effects**: Particle effects and screen shake for impact
- **Sound System**: Procedural audio effects and background music
- **Mobile Support**: Touch controls for mobile devices

## 🎯 How to Play

### Controls
- **WASD / Arrow Keys**: Move your character
- **Space**: Jump
- **X**: Basic Attack
- **Z**: Quirk Attack (special ability)
- **Escape**: Pause game
- **F11**: Toggle fullscreen
- **R**: Restart (when game over)
- **M**: Return to main menu

### Gameplay Tips
- Combine attacks to build up combo multipliers
- Use quirk attacks for area damage and higher impact
- Jump on platforms to avoid enemy attacks
- Your quirk energy regenerates over time
- Level up by defeating enemies to increase your stats
- Different enemy types require different strategies

## 🦹‍♂️ Enemy Types

1. **Basic Villains** (Purple): Standard enemies with balanced stats
2. **Fast Villains** (Red): Quick but fragile enemies
3. **Heavy Villains** (Gray): Slow but powerful tank enemies
4. **Ranged Villains** (Purple): Attack from a distance

## 🎵 Audio Features

- Procedural sound effects using Web Audio API
- Background music with hero theme melody
- Sound effects for jumps, attacks, hits, and explosions
- Toggle sound on/off from the main menu

## 🎨 Visual Style

- Anime-inspired color scheme
- Parallax scrolling background with UA High School
- Particle effects and visual feedback
- Smooth animations and transitions
- Responsive design for different screen sizes

## 🚀 Getting Started

1. **Clone or download** this repository
2. **Serve the files** using a local web server:
   ```bash
   # Using Node.js http-server
   npx http-server -p 8000
   
   # Using Python
   python -m http.server 8000
   
   # Or any other static file server
   ```
3. **Open your browser** and navigate to `http://localhost:8000`
4. **Click "Start Training"** to begin your hero journey!

## 🛠️ Technical Details

### Built With
- **HTML5 Canvas** for rendering
- **Vanilla JavaScript** for game logic
- **Web Audio API** for sound effects
- **CSS3** for UI styling and animations
- **Responsive design** principles

### Architecture
- **Modular design** with separate classes for different game systems
- **Entity-Component pattern** for characters and objects
- **State management** for game flow
- **Input abstraction** supporting both keyboard and touch
- **Audio management** with procedural sound generation

### Performance Features
- **Efficient rendering** with canvas optimization
- **Object pooling** for particles and effects
- **Frame rate monitoring** and optimization
- **Memory management** for smooth gameplay

## 🎭 Easter Eggs

Try typing these cheat codes during gameplay:
- `plusultra` - Restore health and quirk energy
- `allmight` - Gain 1000 bonus points

## 🎓 Educational Value

This game demonstrates:
- **Game development** fundamentals
- **Object-oriented programming** in JavaScript
- **Canvas API** usage and optimization
- **Audio programming** with Web Audio API
- **Responsive web design**
- **State management** patterns
- **Physics simulation** basics

## 🤝 Contributing

Feel free to contribute to this project! Some ideas for improvements:
- Add more quirk types and abilities
- Create additional enemy types
- Implement power-ups and collectibles
- Add more levels and environments
- Enhance visual effects and animations
- Improve mobile touch controls

## 📜 License

This project is for educational purposes. My Hero Academia is a trademark of Kohei Horikoshi and Shueisha.

## 🙏 Acknowledgments

- Inspired by **My Hero Academia** by Kohei Horikoshi
- Built with modern web technologies
- Designed for learning and fun

---

**Plus Ultra!** 🦸‍♂️ Become the hero you were meant to be!
