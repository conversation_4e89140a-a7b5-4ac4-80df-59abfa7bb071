<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Hero Academia - 2D Game</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas" width="1024" height="768"></canvas>
        <div id="ui">
            <div id="mainMenu" class="screen active">
                <h1>My Hero Academia</h1>
                <h2>Hero Training Simulator</h2>
                <div class="menu-buttons">
                    <button id="startBtn" class="menu-btn">Start Training</button>
                    <button id="controlsBtn" class="menu-btn">Controls</button>
                    <button id="soundToggleBtn" class="menu-btn">🔊 Sound: ON</button>
                    <button id="aboutBtn" class="menu-btn">About</button>
                </div>
            </div>
            
            <div id="gameUI" class="screen">
                <div id="hud">
                    <div id="healthBar">
                        <div class="bar-label">HP</div>
                        <div class="bar-container">
                            <div id="healthFill" class="bar-fill"></div>
                        </div>
                    </div>
                    <div id="quirkBar">
                        <div class="bar-label">Quirk</div>
                        <div class="bar-container">
                            <div id="quirkFill" class="bar-fill quirk"></div>
                        </div>
                    </div>
                    <div id="score">Score: <span id="scoreValue">0</span></div>
                </div>
                <div id="controls-hint">
                    <p>WASD/Arrow Keys: Move | Space: Jump | X: Basic Attack | Z: Quirk Attack</p>
                </div>
            </div>
            
            <div id="pauseMenu" class="screen">
                <h2>Game Paused</h2>
                <div class="menu-buttons">
                    <button id="resumeBtn" class="menu-btn">Resume</button>
                    <button id="restartBtn" class="menu-btn">Restart</button>
                    <button id="mainMenuBtn" class="menu-btn">Main Menu</button>
                </div>
            </div>
            
            <div id="gameOverScreen" class="screen">
                <h2>Training Complete!</h2>
                <div id="finalScore">Final Score: <span id="finalScoreValue">0</span></div>
                <div class="menu-buttons">
                    <button id="playAgainBtn" class="menu-btn">Train Again</button>
                    <button id="backToMenuBtn" class="menu-btn">Main Menu</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="js/utils.js"></script>
    <script src="js/input.js"></script>
    <script src="js/audio.js"></script>
    <script src="js/sprite.js"></script>
    <script src="js/character.js"></script>
    <script src="js/player.js"></script>
    <script src="js/enemy.js"></script>
    <script src="js/level.js"></script>
    <script src="js/game.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
