// Enemy classes for the MHA game

class Enemy extends Character {
    constructor(x, y, type = 'basic') {
        super(x, y, 28, 40);
        
        this.type = type;
        this.aiState = 'patrol';
        this.target = null;
        this.detectionRange = 150;
        this.attackRange = 40;
        this.patrolDistance = 100;
        this.patrolCenter = new Vector2(x, y);
        this.patrolDirection = 1;
        this.lastPlayerPosition = null;
        this.searchTime = 0;
        this.aggroTime = 0;
        
        // AI timing
        this.aiUpdateTimer = 0;
        this.aiUpdateInterval = 100; // Update AI every 100ms
        
        this.setupEnemyType();
    }
    
    setupEnemyType() {
        switch (this.type) {
            case 'basic':
                this.setupBasicEnemy();
                break;
            case 'fast':
                this.setupFastEnemy();
                break;
            case 'heavy':
                this.setupHeavyEnemy();
                break;
            case 'ranged':
                this.setupRangedEnemy();
                break;
        }
    }
    
    setupBasicEnemy() {
        this.maxHealth = 60;
        this.health = this.maxHealth;
        this.maxSpeed = 120;
        this.attackDamage = 15;
        this.sprite.addAnimation('idle', ['#9C27B0', '#8E24AA'], 3);
        this.sprite.addAnimation('walk', ['#9C27B0', '#BA68C8', '#9C27B0', '#8E24AA'], 6);
        this.sprite.addAnimation('attack', ['#f44336', '#d32f2f'], 12);
        this.sprite.addAnimation('hurt', ['#ff5722'], 8);
    }
    
    setupFastEnemy() {
        this.maxHealth = 40;
        this.health = this.maxHealth;
        this.maxSpeed = 200;
        this.attackDamage = 12;
        this.detectionRange = 200;
        this.sprite.addAnimation('idle', ['#FF5722', '#F4511E'], 4);
        this.sprite.addAnimation('walk', ['#FF5722', '#FF7043', '#FF5722', '#F4511E'], 10);
        this.sprite.addAnimation('attack', ['#d32f2f', '#b71c1c'], 15);
        this.sprite.addAnimation('hurt', ['#ffab00'], 10);
    }
    
    setupHeavyEnemy() {
        this.maxHealth = 120;
        this.health = this.maxHealth;
        this.maxSpeed = 80;
        this.attackDamage = 30;
        this.attackRange = 60;
        this.sprite.width = 40;
        this.sprite.height = 56;
        this.sprite.addAnimation('idle', ['#424242', '#616161'], 2);
        this.sprite.addAnimation('walk', ['#424242', '#757575', '#424242', '#616161'], 4);
        this.sprite.addAnimation('attack', ['#d32f2f', '#b71c1c', '#424242'], 8);
        this.sprite.addAnimation('hurt', ['#ff9800'], 6);
    }
    
    setupRangedEnemy() {
        this.maxHealth = 50;
        this.health = this.maxHealth;
        this.maxSpeed = 100;
        this.attackDamage = 20;
        this.attackRange = 200;
        this.detectionRange = 250;
        this.sprite.addAnimation('idle', ['#673AB7', '#7B1FA2'], 3);
        this.sprite.addAnimation('walk', ['#673AB7', '#9575CD', '#673AB7', '#7B1FA2'], 6);
        this.sprite.addAnimation('attack', ['#E91E63', '#C2185B'], 10);
        this.sprite.addAnimation('hurt', ['#ff6f00'], 8);
    }
    
    update(deltaTime, level, player) {
        super.update(deltaTime, level);
        
        if (!this.alive) return;
        
        this.target = player;
        this.aiUpdateTimer += deltaTime;
        
        if (this.aiUpdateTimer >= this.aiUpdateInterval) {
            this.updateAI(player);
            this.aiUpdateTimer = 0;
        }
        
        this.executeAI(deltaTime);
    }
    
    updateAI(player) {
        if (!player || !player.alive) {
            this.aiState = 'patrol';
            return;
        }
        
        const distanceToPlayer = this.getCenter().distance(player.getCenter());
        
        switch (this.aiState) {
            case 'patrol':
                if (distanceToPlayer <= this.detectionRange) {
                    this.aiState = 'chase';
                    this.aggroTime = 5000; // Stay aggressive for 5 seconds
                }
                break;
                
            case 'chase':
                if (distanceToPlayer <= this.attackRange) {
                    this.aiState = 'attack';
                } else if (distanceToPlayer > this.detectionRange * 1.5) {
                    this.lastPlayerPosition = player.getCenter();
                    this.aiState = 'search';
                    this.searchTime = 3000; // Search for 3 seconds
                }
                break;
                
            case 'attack':
                if (distanceToPlayer > this.attackRange * 1.2) {
                    this.aiState = 'chase';
                }
                break;
                
            case 'search':
                this.searchTime -= this.aiUpdateInterval;
                if (this.searchTime <= 0) {
                    this.aiState = 'patrol';
                } else if (distanceToPlayer <= this.detectionRange) {
                    this.aiState = 'chase';
                }
                break;
        }
        
        // Reduce aggro time
        if (this.aggroTime > 0) {
            this.aggroTime -= this.aiUpdateInterval;
        }
    }
    
    executeAI(deltaTime) {
        switch (this.aiState) {
            case 'patrol':
                this.patrol();
                break;
            case 'chase':
                this.chasePlayer();
                break;
            case 'attack':
                this.attackPlayer();
                break;
            case 'search':
                this.searchForPlayer();
                break;
        }
    }
    
    patrol() {
        const distanceFromCenter = Math.abs(this.position.x - this.patrolCenter.x);
        
        if (distanceFromCenter >= this.patrolDistance) {
            this.patrolDirection *= -1;
        }
        
        this.move(this.patrolDirection * 0.5);
    }
    
    chasePlayer() {
        if (!this.target) return;
        
        const playerCenter = this.target.getCenter();
        const myCenter = this.getCenter();
        
        if (playerCenter.x > myCenter.x) {
            this.move(1);
        } else if (playerCenter.x < myCenter.x) {
            this.move(-1);
        }
        
        // Jump if player is above and we're on ground
        if (playerCenter.y < myCenter.y - 20 && this.onGround) {
            this.jump();
        }
    }
    
    attackPlayer() {
        if (!this.target || this.attackCooldown > 0) return;
        
        const attackArea = this.attack();
        if (attackArea && window.game) {
            // Check if player is in attack range
            if (attackArea.intersects(this.target.getBounds())) {
                const knockback = new Vector2(this.facing * 100, -50);
                this.target.takeDamage(this.attackDamage, knockback);
                
                // Add hit effect
                window.game.addEffect(new EffectSprite(
                    this.target.getCenter().x,
                    this.target.getCenter().y,
                    'hit'
                ));
            }
        }
    }
    
    searchForPlayer() {
        if (!this.lastPlayerPosition) return;
        
        const myCenter = this.getCenter();
        
        if (this.lastPlayerPosition.x > myCenter.x) {
            this.move(0.3);
        } else if (this.lastPlayerPosition.x < myCenter.x) {
            this.move(-0.3);
        }
    }
    
    takeDamage(damage, knockback = new Vector2(0, 0)) {
        const damaged = super.takeDamage(damage, knockback);
        if (damaged) {
            // Play enemy hit sound
            if (window.audioManager) {
                window.audioManager.playSound('enemyHit');
            }

            // Become aggressive when hit
            this.aiState = 'chase';
            this.aggroTime = 8000;

            // Add hit effect
            if (window.game) {
                window.game.addEffect(new EffectSprite(
                    this.getCenter().x,
                    this.getCenter().y,
                    'hit'
                ));
            }
        }
        return damaged;
    }
    
    die() {
        super.die();

        // Play explosion sound
        if (window.audioManager) {
            window.audioManager.playSound('explosion');
        }

        // Award points to player
        if (window.game && window.game.player) {
            let points = 10;
            switch (this.type) {
                case 'fast': points = 15; break;
                case 'heavy': points = 25; break;
                case 'ranged': points = 20; break;
            }
            window.game.player.addScore(points);
        }

        // Death effect
        if (window.game) {
            window.game.addEffect(new EffectSprite(
                this.getCenter().x,
                this.getCenter().y,
                'explosion'
            ));
        }
    }
}

// Enemy spawner class
class EnemySpawner {
    constructor(level) {
        this.level = level;
        this.enemies = [];
        this.spawnTimer = 0;
        this.spawnInterval = 3000; // 3 seconds
        this.maxEnemies = 8;
        this.waveNumber = 1;
        this.enemiesSpawnedThisWave = 0;
        this.enemiesPerWave = 5;
    }
    
    update(deltaTime, player) {
        this.spawnTimer += deltaTime;
        
        // Remove dead enemies
        this.enemies = this.enemies.filter(enemy => enemy.alive);
        
        // Spawn new enemies
        if (this.spawnTimer >= this.spawnInterval && 
            this.enemies.length < this.maxEnemies &&
            this.enemiesSpawnedThisWave < this.enemiesPerWave) {
            
            this.spawnEnemy(player);
            this.spawnTimer = 0;
        }
        
        // Check for wave completion
        if (this.enemiesSpawnedThisWave >= this.enemiesPerWave && this.enemies.length === 0) {
            this.nextWave();
        }
        
        // Update all enemies
        this.enemies.forEach(enemy => {
            enemy.update(deltaTime, this.level, player);
        });
    }
    
    spawnEnemy(player) {
        // Choose spawn position away from player
        let spawnX;
        const playerX = player ? player.position.x : this.level.width / 2;
        
        if (Math.random() < 0.5) {
            spawnX = Math.random() < 0.5 ? 0 : this.level.width - 50;
        } else {
            spawnX = Utils.randomRange(0, this.level.width - 50);
            // Ensure minimum distance from player
            if (Math.abs(spawnX - playerX) < 200) {
                spawnX = playerX + (spawnX > playerX ? 200 : -200);
                spawnX = Utils.clamp(spawnX, 0, this.level.width - 50);
            }
        }
        
        const spawnY = this.level.groundY - 50;
        
        // Choose enemy type based on wave number
        let enemyType = 'basic';
        const rand = Math.random();
        
        if (this.waveNumber >= 2) {
            if (rand < 0.3) enemyType = 'fast';
            else if (rand < 0.6) enemyType = 'basic';
            else if (rand < 0.8) enemyType = 'ranged';
            else enemyType = 'heavy';
        } else {
            enemyType = rand < 0.7 ? 'basic' : 'fast';
        }
        
        const enemy = new Enemy(spawnX, spawnY, enemyType);
        this.enemies.push(enemy);
        this.enemiesSpawnedThisWave++;
    }
    
    nextWave() {
        this.waveNumber++;
        this.enemiesSpawnedThisWave = 0;
        this.enemiesPerWave += 2;
        this.maxEnemies = Math.min(12, this.maxEnemies + 1);
        this.spawnInterval = Math.max(1500, this.spawnInterval - 200);
        
        // Show wave message
        if (window.game) {
            window.game.showMessage(`Wave ${this.waveNumber}!`, 2000);
        }
    }
    
    draw(ctx) {
        this.enemies.forEach(enemy => {
            enemy.draw(ctx);
        });
    }
    
    getEnemies() {
        return this.enemies;
    }
    
    reset() {
        this.enemies = [];
        this.waveNumber = 1;
        this.enemiesSpawnedThisWave = 0;
        this.enemiesPerWave = 5;
        this.spawnTimer = 0;
        this.maxEnemies = 8;
        this.spawnInterval = 3000;
    }
}
