// Input handling system for the MHA game

class InputManager {
    constructor() {
        this.keys = {};
        this.keysPressed = {};
        this.keysReleased = {};
        
        // Key mappings
        this.keyMap = {
            // Movement
            'ArrowLeft': 'left',
            'KeyA': 'left',
            'ArrowRight': 'right',
            'KeyD': 'right',
            'ArrowUp': 'up',
            'KeyW': 'up',
            'ArrowDown': 'down',
            'KeyS': 'down',
            
            // Actions
            'Space': 'jump',
            'KeyX': 'attack',
            'KeyZ': 'quirk',
            
            // UI
            'Escape': 'pause',
            'Enter': 'confirm'
        };
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (e) => {
            const action = this.keyMap[e.code];
            if (action) {
                e.preventDefault();
                
                if (!this.keys[action]) {
                    this.keysPressed[action] = true;
                }
                this.keys[action] = true;
            }
        });
        
        document.addEventListener('keyup', (e) => {
            const action = this.keyMap[e.code];
            if (action) {
                e.preventDefault();
                this.keys[action] = false;
                this.keysReleased[action] = true;
            }
        });
        
        // Prevent context menu on right click
        document.addEventListener('contextmenu', (e) => e.preventDefault());
        
        // Handle window focus/blur to pause game
        window.addEventListener('blur', () => {
            if (window.game && window.game.state === 'playing') {
                window.game.pause();
            }
        });
    }
    
    // Check if key is currently held down
    isKeyDown(action) {
        return !!this.keys[action];
    }
    
    // Check if key was just pressed this frame
    isKeyPressed(action) {
        return !!this.keysPressed[action];
    }
    
    // Check if key was just released this frame
    isKeyReleased(action) {
        return !!this.keysReleased[action];
    }
    
    // Get movement input as a vector
    getMovementVector() {
        const vector = new Vector2(0, 0);
        
        if (this.isKeyDown('left')) vector.x -= 1;
        if (this.isKeyDown('right')) vector.x += 1;
        if (this.isKeyDown('up')) vector.y -= 1;
        if (this.isKeyDown('down')) vector.y += 1;
        
        // Normalize diagonal movement
        if (vector.x !== 0 && vector.y !== 0) {
            return vector.normalize();
        }
        
        return vector;
    }
    
    // Clear pressed/released states (call at end of frame)
    update() {
        this.keysPressed = {};
        this.keysReleased = {};
    }
    
    // Reset all input states
    reset() {
        this.keys = {};
        this.keysPressed = {};
        this.keysReleased = {};
    }
}

// Touch/Mobile input support
class TouchInputManager {
    constructor(canvas) {
        this.canvas = canvas;
        this.touches = {};
        this.virtualButtons = {
            left: { x: 50, y: canvas.height - 100, radius: 40, pressed: false },
            right: { x: 150, y: canvas.height - 100, radius: 40, pressed: false },
            jump: { x: canvas.width - 100, y: canvas.height - 100, radius: 40, pressed: false },
            attack: { x: canvas.width - 200, y: canvas.height - 100, radius: 40, pressed: false }
        };
        
        this.setupTouchEvents();
    }
    
    setupTouchEvents() {
        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.handleTouchStart(e);
        });
        
        this.canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            this.handleTouchMove(e);
        });
        
        this.canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.handleTouchEnd(e);
        });
    }
    
    handleTouchStart(e) {
        const rect = this.canvas.getBoundingClientRect();
        
        for (let touch of e.changedTouches) {
            const touchPos = {
                x: touch.clientX - rect.left,
                y: touch.clientY - rect.top
            };
            
            // Check virtual buttons
            for (let [buttonName, button] of Object.entries(this.virtualButtons)) {
                const distance = Math.sqrt(
                    Math.pow(touchPos.x - button.x, 2) + 
                    Math.pow(touchPos.y - button.y, 2)
                );
                
                if (distance <= button.radius) {
                    button.pressed = true;
                    this.touches[touch.identifier] = buttonName;
                    break;
                }
            }
        }
    }
    
    handleTouchMove(e) {
        // Handle touch movement if needed
    }
    
    handleTouchEnd(e) {
        for (let touch of e.changedTouches) {
            const buttonName = this.touches[touch.identifier];
            if (buttonName && this.virtualButtons[buttonName]) {
                this.virtualButtons[buttonName].pressed = false;
                delete this.touches[touch.identifier];
            }
        }
    }
    
    // Draw virtual buttons on mobile
    draw(ctx) {
        if (!this.isMobile()) return;
        
        ctx.save();
        ctx.globalAlpha = 0.5;
        
        for (let [name, button] of Object.entries(this.virtualButtons)) {
            ctx.fillStyle = button.pressed ? '#ff6b35' : '#ffffff';
            ctx.beginPath();
            ctx.arc(button.x, button.y, button.radius, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = '#000000';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(name.toUpperCase(), button.x, button.y + 4);
        }
        
        ctx.restore();
    }
    
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    
    isButtonPressed(buttonName) {
        return this.virtualButtons[buttonName] && this.virtualButtons[buttonName].pressed;
    }
}
