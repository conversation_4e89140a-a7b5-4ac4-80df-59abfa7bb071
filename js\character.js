// Base character class for the MHA game

class Character {
    constructor(x, y, width = 32, height = 48) {
        this.sprite = new AnimatedSprite(x, y, width, height);
        this.position = this.sprite.position;
        this.velocity = new Vector2(0, 0);
        this.acceleration = new Vector2(0, 0);
        
        // Physics properties
        this.maxSpeed = 200;
        this.jumpPower = 400;
        this.gravity = 800;
        this.friction = 0.8;
        this.onGround = false;
        
        // Character stats
        this.maxHealth = 100;
        this.health = this.maxHealth;
        this.maxQuirkEnergy = 100;
        this.quirkEnergy = this.maxQuirkEnergy;
        this.quirkRegenRate = 20; // per second
        
        // Combat properties
        this.attackDamage = 20;
        this.quirkDamage = 40;
        this.attackRange = 50;
        this.attackCooldown = 0;
        this.quirkCooldown = 0;
        this.invulnerable = false;
        this.invulnerabilityTime = 0;
        
        // State management
        this.state = 'idle';
        this.facing = 1; // 1 for right, -1 for left
        this.alive = true;
        
        this.setupAnimations();
    }
    
    setupAnimations() {
        // Define color animations for different states
        this.sprite.addAnimation('idle', ['#4CAF50', '#66BB6A'], 2);
        this.sprite.addAnimation('walk', ['#4CAF50', '#81C784', '#4CAF50', '#66BB6A'], 8);
        this.sprite.addAnimation('jump', ['#2196F3'], 1);
        this.sprite.addAnimation('attack', ['#ff6b35', '#f7931e'], 12);
        this.sprite.addAnimation('quirk', ['#9C27B0', '#E91E63', '#2196F3'], 15);
        this.sprite.addAnimation('hurt', ['#f44336'], 8);
        this.sprite.addAnimation('dead', ['#424242'], 1);
    }
    
    update(deltaTime, level) {
        if (!this.alive) return;
        
        // Update cooldowns
        this.attackCooldown = Math.max(0, this.attackCooldown - deltaTime / 1000);
        this.quirkCooldown = Math.max(0, this.quirkCooldown - deltaTime / 1000);
        this.invulnerabilityTime = Math.max(0, this.invulnerabilityTime - deltaTime / 1000);
        this.invulnerable = this.invulnerabilityTime > 0;
        
        // Regenerate quirk energy
        this.quirkEnergy = Math.min(this.maxQuirkEnergy, 
            this.quirkEnergy + this.quirkRegenRate * deltaTime / 1000);
        
        // Apply physics
        this.updatePhysics(deltaTime, level);
        
        // Update animation state
        this.updateAnimationState();
        
        // Update sprite
        this.sprite.update(deltaTime);
        
        // Check if character is dead
        if (this.health <= 0 && this.alive) {
            this.die();
        }
    }
    
    updatePhysics(deltaTime, level) {
        const dt = deltaTime / 1000;
        
        // Apply gravity
        if (!this.onGround) {
            this.velocity.y += this.gravity * dt;
        }
        
        // Apply friction
        this.velocity.x *= this.friction;
        
        // Limit horizontal speed
        this.velocity.x = Utils.clamp(this.velocity.x, -this.maxSpeed, this.maxSpeed);
        
        // Update position
        const newPosition = this.position.add(this.velocity.multiply(dt));
        
        // Collision detection with level bounds
        if (level) {
            // Ground collision
            if (newPosition.y + this.sprite.height >= level.groundY) {
                newPosition.y = level.groundY - this.sprite.height;
                this.velocity.y = 0;
                this.onGround = true;
            } else {
                this.onGround = false;
            }
            
            // Side boundaries
            newPosition.x = Utils.clamp(newPosition.x, 0, level.width - this.sprite.width);
        }
        
        this.position.x = newPosition.x;
        this.position.y = newPosition.y;
        this.sprite.position = this.position;
    }
    
    updateAnimationState() {
        let newState = 'idle';
        
        if (!this.alive) {
            newState = 'dead';
        } else if (this.invulnerable) {
            newState = 'hurt';
        } else if (this.state === 'attacking' && this.attackCooldown > 0.3) {
            newState = 'attack';
        } else if (this.state === 'quirking' && this.quirkCooldown > 1.5) {
            newState = 'quirk';
        } else if (!this.onGround) {
            newState = 'jump';
        } else if (Math.abs(this.velocity.x) > 10) {
            newState = 'walk';
        }
        
        this.sprite.playAnimation(newState);
        
        // Update sprite facing direction
        if (this.velocity.x > 0) {
            this.facing = 1;
            this.sprite.flipX = false;
        } else if (this.velocity.x < 0) {
            this.facing = -1;
            this.sprite.flipX = true;
        }
    }
    
    move(direction) {
        if (!this.alive) return;
        
        const moveForce = 300;
        this.velocity.x += direction * moveForce * (1/60); // Assuming 60 FPS
    }
    
    jump() {
        if (!this.alive || !this.onGround) return false;
        
        this.velocity.y = -this.jumpPower;
        this.onGround = false;
        return true;
    }
    
    attack() {
        if (!this.alive || this.attackCooldown > 0) return null;
        
        this.state = 'attacking';
        this.attackCooldown = 0.5;
        
        // Create attack hitbox
        const attackX = this.position.x + (this.facing > 0 ? this.sprite.width : -this.attackRange);
        const attackY = this.position.y;
        
        return new Rectangle(attackX, attackY, this.attackRange, this.sprite.height);
    }
    
    useQuirk() {
        if (!this.alive || this.quirkCooldown > 0 || this.quirkEnergy < 30) return null;
        
        this.state = 'quirking';
        this.quirkCooldown = 2.0;
        this.quirkEnergy -= 30;
        
        // Create quirk effect area (larger than basic attack)
        const quirkRange = this.attackRange * 1.5;
        const quirkX = this.position.x + (this.facing > 0 ? this.sprite.width : -quirkRange);
        const quirkY = this.position.y - 20;
        
        return new Rectangle(quirkX, quirkY, quirkRange, this.sprite.height + 40);
    }
    
    takeDamage(damage, knockback = new Vector2(0, 0)) {
        if (!this.alive || this.invulnerable) return false;
        
        this.health = Math.max(0, this.health - damage);
        this.invulnerabilityTime = 1.0;
        
        // Apply knockback
        this.velocity = this.velocity.add(knockback);
        
        return true;
    }
    
    heal(amount) {
        if (!this.alive) return;
        
        this.health = Math.min(this.maxHealth, this.health + amount);
    }
    
    die() {
        this.alive = false;
        this.velocity = new Vector2(0, 0);
        this.state = 'dead';
    }
    
    getBounds() {
        return this.sprite.getBounds();
    }
    
    getCenter() {
        return this.sprite.getCenter();
    }
    
    draw(ctx) {
        // Flash effect when invulnerable
        if (this.invulnerable && Math.floor(Date.now() / 100) % 2) {
            return; // Skip drawing to create flashing effect
        }
        
        this.sprite.draw(ctx);
        
        // Draw health bar above character
        if (this.health < this.maxHealth && this.alive) {
            this.drawHealthBar(ctx);
        }
    }
    
    drawHealthBar(ctx) {
        const barWidth = this.sprite.width;
        const barHeight = 4;
        const barX = this.position.x;
        const barY = this.position.y - 10;
        
        // Background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(barX, barY, barWidth, barHeight);
        
        // Health fill
        const healthPercent = this.health / this.maxHealth;
        const fillWidth = barWidth * healthPercent;
        
        ctx.fillStyle = healthPercent > 0.5 ? '#4CAF50' : 
                       healthPercent > 0.25 ? '#ff9800' : '#f44336';
        ctx.fillRect(barX, barY, fillWidth, barHeight);
        
        // Border
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 1;
        ctx.strokeRect(barX, barY, barWidth, barHeight);
    }
}
