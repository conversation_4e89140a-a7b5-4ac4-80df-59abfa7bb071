@import url('https://fonts.googleapis.com/css2?family=Bangers&family=Roboto:wght@400;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    overflow: hidden;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

#gameContainer {
    position: relative;
    border: 3px solid #fff;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    background: #000;
}

#gameCanvas {
    display: block;
    background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
}

#ui {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    pointer-events: all;
}

.screen.active {
    display: flex;
}

/* Main Menu Styles */
#mainMenu {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    text-align: center;
}

#mainMenu h1 {
    font-family: 'Bangers', cursive;
    font-size: 4rem;
    color: #ff6b35;
    text-shadow: 3px 3px 0px #000;
    margin-bottom: 10px;
}

#mainMenu h2 {
    font-size: 1.5rem;
    color: #ffd700;
    margin-bottom: 40px;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.menu-btn {
    padding: 15px 30px;
    font-size: 1.2rem;
    font-weight: bold;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
    background: linear-gradient(45deg, #f7931e, #ff6b35);
}

/* Game UI Styles */
#gameUI {
    pointer-events: none;
}

#hud {
    position: absolute;
    top: 20px;
    left: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    pointer-events: none;
}

#healthBar, #quirkBar {
    display: flex;
    align-items: center;
    gap: 10px;
}

.bar-label {
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    min-width: 40px;
}

.bar-container {
    width: 200px;
    height: 20px;
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid #fff;
    border-radius: 10px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    transition: width 0.3s ease;
    width: 100%;
}

.bar-fill.quirk {
    background: linear-gradient(90deg, #2196F3, #03DAC6);
}

#score {
    color: white;
    font-weight: bold;
    font-size: 18px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

#controls-hint {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 12px;
    text-align: center;
}

/* Pause and Game Over Screens */
#pauseMenu, #gameOverScreen {
    background: rgba(0, 0, 0, 0.9);
    color: white;
    text-align: center;
}

#pauseMenu h2, #gameOverScreen h2 {
    font-family: 'Bangers', cursive;
    font-size: 3rem;
    color: #ff6b35;
    text-shadow: 2px 2px 0px #000;
    margin-bottom: 30px;
}

#finalScore {
    font-size: 1.5rem;
    color: #ffd700;
    margin-bottom: 30px;
}

/* Animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes glow {
    0% { box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3); }
    50% { box-shadow: 0 6px 25px rgba(255, 107, 53, 0.6); }
    100% { box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3); }
}

@keyframes slideInFromTop {
    0% { transform: translateY(-100px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

/* Apply animations */
#mainMenu h1 {
    animation: slideInFromTop 1s ease-out, glow 2s infinite;
}

#mainMenu h2 {
    animation: fadeIn 1.5s ease-out;
}

.menu-btn {
    animation: fadeIn 2s ease-out;
    transition: all 0.3s ease, transform 0.2s ease;
}

.menu-btn:hover {
    animation: pulse 0.6s infinite;
}

.bar-fill {
    transition: width 0.5s ease;
}

/* Loading animation for health/quirk bars */
.bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    #gameCanvas {
        width: 800px;
        height: 600px;
    }
}

@media (max-width: 900px) {
    #gameCanvas {
        width: 640px;
        height: 480px;
    }

    #mainMenu h1 {
        font-size: 3rem;
    }

    .menu-btn {
        padding: 12px 25px;
        font-size: 1rem;
    }
}
