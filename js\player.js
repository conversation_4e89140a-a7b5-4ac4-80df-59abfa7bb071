// Player character class for the MHA game

class Player extends Character {
    constructor(x, y) {
        super(x, y, 32, 48);
        
        // Player-specific properties
        this.score = 0;
        this.experience = 0;
        this.level = 1;
        this.quirkType = 'explosion'; // Default quirk
        this.comboCount = 0;
        this.lastAttackTime = 0;
        
        // Enhanced stats for player
        this.maxSpeed = 250;
        this.jumpPower = 450;
        this.attackDamage = 25;
        this.quirkDamage = 50;
        
        // Player-specific animations with hero colors
        this.setupPlayerAnimations();
    }
    
    setupPlayerAnimations() {
        // Hero-themed color schemes
        this.sprite.addAnimation('idle', ['#2196F3', '#1976D2'], 3);
        this.sprite.addAnimation('walk', ['#2196F3', '#42A5F5', '#2196F3', '#1976D2'], 8);
        this.sprite.addAnimation('jump', ['#03DAC6'], 1);
        this.sprite.addAnimation('attack', ['#ff6b35', '#f7931e', '#ffab00'], 15);
        this.sprite.addAnimation('quirk', ['#9C27B0', '#E91E63', '#ff6b35', '#ffab00'], 20);
        this.sprite.addAnimation('hurt', ['#f44336'], 10);
        this.sprite.addAnimation('victory', ['#ffd700', '#ffeb3b'], 5);
    }
    
    update(deltaTime, level, inputManager) {
        super.update(deltaTime, level);
        
        if (!this.alive) return;
        
        // Handle input
        this.handleInput(inputManager);
        
        // Update combo system
        this.updateCombo(deltaTime);
        
        // Update state transitions
        if (this.state === 'attacking' && this.attackCooldown <= 0) {
            this.state = 'idle';
        }
        if (this.state === 'quirking' && this.quirkCooldown <= 0) {
            this.state = 'idle';
        }
    }
    
    handleInput(inputManager) {
        // Movement
        const movement = inputManager.getMovementVector();
        if (movement.x !== 0) {
            this.move(movement.x);
        }
        
        // Jump
        if (inputManager.isKeyPressed('jump')) {
            if (this.jump()) {
                // Play jump sound
                if (window.audioManager) {
                    window.audioManager.playSound('jump');
                }

                // Add some upward visual effect
                if (window.game) {
                    window.game.addEffect(new EffectSprite(
                        this.getCenter().x,
                        this.position.y + this.sprite.height,
                        'quirk'
                    ));
                }
            }
        }
        
        // Basic attack
        if (inputManager.isKeyPressed('attack')) {
            const attackArea = this.attack();
            if (attackArea && window.game) {
                // Play attack sound
                if (window.audioManager) {
                    window.audioManager.playSound('attack');
                }
                window.game.handlePlayerAttack(attackArea, this.attackDamage, false);
            }
        }

        // Quirk attack
        if (inputManager.isKeyPressed('quirk')) {
            const quirkArea = this.useQuirk();
            if (quirkArea && window.game) {
                // Play quirk sound
                if (window.audioManager) {
                    window.audioManager.playSound('quirk');
                }

                window.game.handlePlayerAttack(quirkArea, this.quirkDamage, true);

                // Add quirk visual effect
                window.game.addEffect(new EffectSprite(
                    this.getCenter().x + this.facing * 30,
                    this.getCenter().y,
                    'quirk'
                ));
            }
        }
    }
    
    updateCombo(deltaTime) {
        // Reset combo if too much time has passed since last attack
        if (Date.now() - this.lastAttackTime > 3000) {
            this.comboCount = 0;
        }
    }
    
    attack() {
        const attackArea = super.attack();
        if (attackArea) {
            this.lastAttackTime = Date.now();
            this.comboCount++;
            
            // Add attack visual effect
            if (window.game) {
                window.game.addEffect(new EffectSprite(
                    this.getCenter().x + this.facing * 25, 
                    this.getCenter().y, 
                    'hit'
                ));
            }
        }
        return attackArea;
    }
    
    useQuirk() {
        const quirkArea = super.useQuirk();
        if (quirkArea) {
            this.lastAttackTime = Date.now();
            this.comboCount += 2; // Quirk attacks give more combo
        }
        return quirkArea;
    }
    
    addScore(points) {
        this.score += points * Math.max(1, this.comboCount);
        this.experience += points;
        
        // Level up system
        const expNeeded = this.level * 100;
        if (this.experience >= expNeeded) {
            this.levelUp();
        }
    }
    
    levelUp() {
        this.level++;
        this.experience = 0;

        // Increase stats
        this.maxHealth += 20;
        this.health = this.maxHealth; // Full heal on level up
        this.maxQuirkEnergy += 10;
        this.quirkEnergy = this.maxQuirkEnergy;
        this.attackDamage += 5;
        this.quirkDamage += 10;

        // Play level up sound
        if (window.audioManager) {
            window.audioManager.playSound('levelUp');
        }

        // Visual effect
        if (window.game) {
            window.game.addEffect(new EffectSprite(
                this.getCenter().x,
                this.getCenter().y,
                'quirk'
            ));

            // Show level up message
            window.game.showMessage(`LEVEL UP! Level ${this.level}`, 2000);
        }
    }
    
    takeDamage(damage, knockback = new Vector2(0, 0)) {
        const damaged = super.takeDamage(damage, knockback);
        if (damaged) {
            // Reset combo on taking damage
            this.comboCount = 0;
            
            // Screen shake effect
            if (window.game) {
                window.game.addScreenShake(10, 300);
            }
        }
        return damaged;
    }
    
    // Special quirk abilities based on quirk type
    activateSpecialQuirk() {
        if (this.quirkEnergy < 50) return false;
        
        this.quirkEnergy -= 50;
        
        switch (this.quirkType) {
            case 'explosion':
                return this.explosionQuirk();
            case 'ice':
                return this.iceQuirk();
            case 'lightning':
                return this.lightningQuirk();
            default:
                return this.explosionQuirk();
        }
    }
    
    explosionQuirk() {
        // Create multiple explosion effects around the player
        if (window.game) {
            const center = this.getCenter();
            for (let i = 0; i < 8; i++) {
                const angle = (i / 8) * Math.PI * 2;
                const distance = 60;
                const x = center.x + Math.cos(angle) * distance;
                const y = center.y + Math.sin(angle) * distance;
                
                window.game.addEffect(new EffectSprite(x, y, 'explosion'));
                
                // Damage enemies in explosion radius
                const explosionArea = new Rectangle(x - 30, y - 30, 60, 60);
                window.game.handlePlayerAttack(explosionArea, this.quirkDamage * 1.5, true);
            }
        }
        return true;
    }
    
    iceQuirk() {
        // Create ice wall effect
        if (window.game) {
            const center = this.getCenter();
            for (let i = 0; i < 5; i++) {
                const x = center.x + (i - 2) * 40;
                window.game.addEffect(new EffectSprite(x, center.y, 'quirk'));
                
                // Slow and damage enemies
                const iceArea = new Rectangle(x - 20, center.y - 40, 40, 80);
                window.game.handlePlayerAttack(iceArea, this.quirkDamage * 0.8, true);
            }
        }
        return true;
    }
    
    lightningQuirk() {
        // Create lightning strike effect
        if (window.game) {
            const center = this.getCenter();
            
            // Multiple lightning strikes
            for (let i = 0; i < 3; i++) {
                const x = center.x + Utils.randomRange(-100, 100);
                const y = center.y + Utils.randomRange(-50, 50);
                
                window.game.addEffect(new EffectSprite(x, y, 'quirk'));
                
                // High damage, small area
                const lightningArea = new Rectangle(x - 15, y - 60, 30, 120);
                window.game.handlePlayerAttack(lightningArea, this.quirkDamage * 2, true);
            }
        }
        return true;
    }
    
    draw(ctx) {
        super.draw(ctx);
        
        // Draw combo indicator
        if (this.comboCount > 1) {
            this.drawComboIndicator(ctx);
        }
    }
    
    drawComboIndicator(ctx) {
        const comboX = this.position.x + this.sprite.width / 2;
        const comboY = this.position.y - 25;
        
        ctx.save();
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.fillStyle = '#ffff00';
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        
        const comboText = `${this.comboCount}x COMBO!`;
        ctx.strokeText(comboText, comboX, comboY);
        ctx.fillText(comboText, comboX, comboY);
        
        ctx.restore();
    }
    
    reset() {
        // Reset player to initial state
        this.health = this.maxHealth;
        this.quirkEnergy = this.maxQuirkEnergy;
        this.velocity = new Vector2(0, 0);
        this.alive = true;
        this.state = 'idle';
        this.comboCount = 0;
        this.attackCooldown = 0;
        this.quirkCooldown = 0;
        this.invulnerabilityTime = 0;
    }
}
